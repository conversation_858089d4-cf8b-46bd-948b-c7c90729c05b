<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Janmejay Tiwari - Portfolio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <main class="hero-section">
        <div class="container">
            <div class="hero-content">
                <!-- Left Column - Personal Image -->
                <div class="image-column">
                    <div class="image-placeholder" aria-label="Personal Image Placeholder">
                        <span class="placeholder-text">Personal Image</span>
                    </div>
                </div>
                
                <!-- Right Column - Content -->
                <div class="content-column">
                    <div class="content-wrapper">
                        <!-- Name -->
                        <h1 class="hero-name" data-animate="name">Janmejay Tiwari</h1>
                        
                        <!-- Bio -->
                        <p class="hero-bio" data-animate="bio">A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.</p>
                        
                        <!-- Buttons -->
                        <div class="buttons-container" data-animate="buttons">
                            <button class="btn btn--primary btn-view-work" aria-label="View my work portfolio">
                                View Work
                            </button>
                            <a href="https://drive.google.com/file/d/1qnpzw2s0qeaB8ue1Fv4O--Iq0oIrfMgk/view?usp=sharing" 
                               target="_blank" 
                               rel="noopener noreferrer" 
                               class="btn btn--outline btn-resume"
                               aria-label="View my resume (opens in new tab)">
                                View Resume
                            </a>
                        </div>
                        
                        <!-- Social Icons -->
                        <div class="social-icons" data-animate="social">
                            <a href="https://github.com" target="_blank" rel="noopener noreferrer" class="social-icon" aria-label="GitHub Profile">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                            <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" class="social-icon" aria-label="LinkedIn Profile">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="social-icon" aria-label="Twitter Profile">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                            <a href="mailto:<EMAIL>" class="social-icon" aria-label="Send Email">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h3.819l6.545 4.91 6.545-4.91h3.819A1.636 1.636 0 0 1 24 5.457z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Work Experience Section -->
    <section class="work-experience-section">
        <div class="container">
            <h2 class="section-title" data-animate="work-title">Work Experience</h2>

            <div class="timeline-container">
                <!-- Central Timeline Line -->
                <div class="timeline-line"></div>

                <!-- Timeline Item 1 - Left Side -->
                <div class="timeline-item timeline-item--left" data-animate="timeline-item-1">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="company-header">
                            <div class="company-logo">
                                <span class="logo-placeholder">CT</span>
                            </div>
                            <h3 class="company-name">Celebal Technologies</h3>
                        </div>
                        <h4 class="job-role">React Developer Intern</h4>
                        <p class="job-timeline">
                            <svg class="calendar-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                            </svg>
                            May 2025 - July 2025
                        </p>
                        <ul class="job-details">
                            <li>Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement.</li>
                            <li>Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS.</li>
                            <li><a href="#" class="certificate-btn" target="_blank" rel="noopener noreferrer">View Certificate</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Timeline Item 2 - Right Side -->
                <div class="timeline-item timeline-item--right" data-animate="timeline-item-2">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="company-header">
                            <div class="company-logo">
                                <span class="logo-placeholder">1S</span>
                            </div>
                            <h3 class="company-name">1stop.ai</h3>
                        </div>
                        <h4 class="job-role">Frontend Developer Intern</h4>
                        <p class="job-timeline">
                            <svg class="calendar-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                            </svg>
                            Jan 2025 - Apr 2025
                        </p>
                        <ul class="job-details">
                            <li>Collaborated with a team to develop responsive web application using MERN stack.</li>
                            <li>Implemented frontend components using React.js.</li>
                            <li>Assisted in database design and implementation with Firebase.</li>
                            <li>Integrated AWS S3 for media storage and content delivery.</li>
                            <li><a href="#" class="certificate-btn" target="_blank" rel="noopener noreferrer">View Certificate</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Work Section -->
    <section class="project-work-section">
        <div class="container">
            <h2 class="section-title" data-animate="project-title">Project Work</h2>

            <div class="project-timeline-container">
                <!-- Vertical Timeline Line -->
                <div class="project-timeline-line"></div>

                <!-- Project 1: Web accessibility analyser -->
                <div class="project-timeline-item" data-animate="project-item-1">
                    <div class="project-marker"></div>
                    <div class="project-content">
                        <h3 class="project-name">Web accessibility analyser</h3>
                        <p class="project-purpose">A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions.</p>
                        <div class="project-tech-stack">
                            <span class="tech-pill">React</span>
                            <span class="tech-pill">Node.js</span>
                            <span class="tech-pill">AI/ML</span>
                            <span class="tech-pill">Accessibility APIs</span>
                            <span class="tech-pill">Express.js</span>
                        </div>
                        <p class="project-learnings">Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers.</p>
                        <a href="https://github.com/Janmejay3108/Accessibility-analyzer" target="_blank" rel="noopener noreferrer" class="project-link" aria-label="View Web accessibility analyser on GitHub">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Project 2: Secure File Sharing Platform -->
                <div class="project-timeline-item" data-animate="project-item-2">
                    <div class="project-marker"></div>
                    <div class="project-content">
                        <h3 class="project-name">Secure File Sharing Platform</h3>
                        <p class="project-purpose">A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI.</p>
                        <div class="project-tech-stack">
                            <span class="tech-pill">React</span>
                            <span class="tech-pill">Node.js</span>
                            <span class="tech-pill">Express.js</span>
                            <span class="tech-pill">MongoDB</span>
                            <span class="tech-pill">Docker</span>
                            <span class="tech-pill">i18n</span>
                        </div>
                        <p class="project-learnings">Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker.</p>
                        <a href="https://github.com/Janmejay3108/File_Transfer_application" target="_blank" rel="noopener noreferrer" class="project-link" aria-label="View Secure File Sharing Platform on GitHub">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Education & Certificates Section -->
    <section class="education-certificates-section">
        <div class="container">
            <h2 class="section-title" data-animate="education-title">Education & Certificates</h2>

            <!-- Tab Navigation -->
            <div class="tab-navigation" data-animate="tab-nav">
                <button class="tab-button active" data-tab="certificates" aria-label="View Certificates">
                    Certificates
                </button>
                <button class="tab-button" data-tab="education" aria-label="View Education">
                    Education
                </button>
            </div>

            <!-- Tab Content Container -->
            <div class="tab-content-container">
                <!-- Certificates View -->
                <div class="tab-content active" data-content="certificates">
                    <div class="timeline-view-container">
                        <!-- Vertical Timeline Line -->
                        <div class="vertical-timeline-line"></div>

                        <!-- Certificate Items -->
                        <div class="timeline-view-item" data-animate="cert-item-1">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="certificate-name">AWS Cloud Solutions Architect</h4>
                                <div class="certificate-details">
                                    <div class="certificate-logo">AWS</div>
                                    <span class="certificate-issuer">Amazon Web Services</span>
                                    <span class="certificate-platform">Forage</span>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-view-item" data-animate="cert-item-2">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="certificate-name">Full Stack Developer</h4>
                                <div class="certificate-details">
                                    <div class="certificate-logo">IBM</div>
                                    <span class="certificate-issuer">IBM</span>
                                    <span class="certificate-platform">Coursera</span>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-view-item" data-animate="cert-item-3">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="certificate-name">Database and SQL for data science with Python</h4>
                                <div class="certificate-details">
                                    <div class="certificate-logo">IBM</div>
                                    <span class="certificate-issuer">IBM</span>
                                    <span class="certificate-platform">Coursera</span>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-view-item" data-animate="cert-item-4">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="certificate-name">RAG and Agentic AI</h4>
                                <div class="certificate-details">
                                    <div class="certificate-logo">IBM</div>
                                    <span class="certificate-issuer">IBM</span>
                                    <span class="certificate-platform">Coursera</span>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-view-item" data-animate="cert-item-5">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="certificate-name">Artificial Intelligence Essentials</h4>
                                <div class="certificate-details">
                                    <div class="certificate-logo">GGL</div>
                                    <span class="certificate-issuer">Google</span>
                                    <span class="certificate-platform">Coursera</span>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-view-item" data-animate="cert-item-6">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="certificate-name">Convolutional Neural Networks</h4>
                                <div class="certificate-details">
                                    <div class="certificate-logo">DL</div>
                                    <span class="certificate-issuer">Deeplearning.AI</span>
                                    <span class="certificate-platform">Coursera</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Education View -->
                <div class="tab-content" data-content="education">
                    <div class="timeline-view-container">
                        <!-- Vertical Timeline Line -->
                        <div class="vertical-timeline-line"></div>

                        <!-- Education Items -->
                        <div class="timeline-view-item" data-animate="edu-item-1">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="education-degree">B.Tech in Electronics and Communication Engineering</h4>
                                <p class="education-institution">Institute of Engineering and Management, Kolkata</p>
                                <p class="education-details">CGPA: 9.38 | Timeline: 2022-2026</p>
                            </div>
                        </div>

                        <div class="timeline-view-item" data-animate="edu-item-2">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="education-degree">Class XII</h4>
                                <p class="education-institution">Abhinav Bharati High School, Kolkata</p>
                                <p class="education-details">Score: 76.83% | Year: 2022</p>
                            </div>
                        </div>

                        <div class="timeline-view-item" data-animate="edu-item-3">
                            <div class="timeline-view-marker"></div>
                            <div class="timeline-view-content">
                                <h4 class="education-degree">Class X</h4>
                                <p class="education-institution">Abhinav Bharati High School, Kolkata</p>
                                <p class="education-details">Score: 76% | Year: 2020</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section class="skills-section">
        <div class="container">
            <div class="skills-header">
                <svg class="skills-icon" width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 14H4v-4h11v4zm0-5H4V9h11v4zm5 5h-4V9h4v9z"/>
                </svg>
                <h2 class="section-title" data-animate="skills-title">Skills</h2>
            </div>

            <div class="skills-content">
                <!-- Left Column: Skills Timeline -->
                <div class="skills-timeline-column">
                    <div class="skills-timeline-container">
                        <!-- Vertical Timeline Line -->
                        <div class="skills-timeline-line"></div>

                        <!-- Languages -->
                        <div class="skills-timeline-item" data-animate="skill-category-1">
                            <div class="skills-timeline-marker"></div>
                            <div class="skills-timeline-content">
                                <h4 class="skill-category-name">Languages</h4>
                                <p class="skill-category-list">Java, C++, Python, JavaScript, TypeScript, SQL</p>
                            </div>
                        </div>

                        <!-- Web Development -->
                        <div class="skills-timeline-item" data-animate="skill-category-2">
                            <div class="skills-timeline-marker"></div>
                            <div class="skills-timeline-content">
                                <h4 class="skill-category-name">Web Development</h4>
                                <p class="skill-category-list">HTML, CSS, React.js, Angular, Spring Boot, Express.js, Next.js, Vite, MERN Stack, Redux</p>
                            </div>
                        </div>

                        <!-- Database Technologies -->
                        <div class="skills-timeline-item" data-animate="skill-category-3">
                            <div class="skills-timeline-marker"></div>
                            <div class="skills-timeline-content">
                                <h4 class="skill-category-name">Database Technologies</h4>
                                <p class="skill-category-list">MongoDB, SQL, PostgreSQL, Firebase, Supabase</p>
                            </div>
                        </div>

                        <!-- Cloud & DevOps -->
                        <div class="skills-timeline-item" data-animate="skill-category-4">
                            <div class="skills-timeline-marker"></div>
                            <div class="skills-timeline-content">
                                <h4 class="skill-category-name">Cloud & DevOps</h4>
                                <p class="skill-category-list">AWS (EC2, S3, Lambda), Docker, CI/CD pipelines</p>
                            </div>
                        </div>

                        <!-- AI & Machine Learning -->
                        <div class="skills-timeline-item" data-animate="skill-category-5">
                            <div class="skills-timeline-marker"></div>
                            <div class="skills-timeline-content">
                                <h4 class="skill-category-name">AI & Machine Learning</h4>
                                <p class="skill-category-list">RAG (Retrieval-Augmented Generation), Agentic AI Workflows, LLM Integration, Vector Databases</p>
                            </div>
                        </div>

                        <!-- Operating Systems -->
                        <div class="skills-timeline-item" data-animate="skill-category-6">
                            <div class="skills-timeline-marker"></div>
                            <div class="skills-timeline-content">
                                <h4 class="skill-category-name">Operating Systems</h4>
                                <p class="skill-category-list">Linux, OS, Android</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Interactive Word Cloud -->
                <div class="skills-wordcloud-column">
                    <div class="wordcloud-container" data-animate="wordcloud">
                        <div class="wordcloud-sphere" id="wordcloud-sphere">
                            <span class="wordcloud-item large" data-skill="React.js">React.js</span>
                            <span class="wordcloud-item large" data-skill="AWS">AWS</span>
                            <span class="wordcloud-item medium" data-skill="Next.js">Next.js</span>
                            <span class="wordcloud-item medium" data-skill="Docker">Docker</span>
                            <span class="wordcloud-item medium" data-skill="Python">Python</span>
                            <span class="wordcloud-item medium" data-skill="TypeScript">TypeScript</span>
                            <span class="wordcloud-item small" data-skill="MongoDB">MongoDB</span>
                            <span class="wordcloud-item small" data-skill="RAG">RAG</span>
                            <span class="wordcloud-item small" data-skill="SQL">SQL</span>
                            <span class="wordcloud-item small" data-skill="Spring Boot">Spring Boot</span>
                            <span class="wordcloud-item small" data-skill="Express.js">Express.js</span>
                            <span class="wordcloud-item small" data-skill="JavaScript">JavaScript</span>
                            <span class="wordcloud-item small" data-skill="Node.js">Node.js</span>
                            <span class="wordcloud-item small" data-skill="Redux">Redux</span>
                            <span class="wordcloud-item small" data-skill="Firebase">Firebase</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="app.js"></script>
</body>
</html>