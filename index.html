<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Portfolio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">

    <!-- React and Framer Motion CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/framer-motion@10.16.4/dist/framer-motion.umd.js"></script>
</head>
<body>
    <main class="hero-section">
        <div class="container">
            <div class="hero-content">
                <!-- Left Column - Personal Image -->
                <div class="image-column">
                    <div class="image-placeholder" aria-label="Personal Image Placeholder">
                        <span class="placeholder-text">Personal Image</span>
                    </div>
                </div>
                
                <!-- Right Column - Content -->
                <div class="content-column">
                    <div class="content-wrapper">
                        <!-- Name -->
                        <h1 class="hero-name" data-animate="name">Janmejay Tiwari</h1>
                        
                        <!-- Bio -->
                        <p class="hero-bio" data-animate="bio">A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.</p>
                        
                        <!-- Buttons -->
                        <div class="buttons-container" data-animate="buttons">
                            <button class="btn btn--primary btn-view-work" aria-label="View my work portfolio">
                                View Work
                            </button>
                            <a href="https://drive.google.com/file/d/1qnpzw2s0qeaB8ue1Fv4O--Iq0oIrfMgk/view?usp=sharing" 
                               target="_blank" 
                               rel="noopener noreferrer" 
                               class="btn btn--outline btn-resume"
                               aria-label="View my resume (opens in new tab)">
                                View Resume
                            </a>
                        </div>
                        
                        <!-- Social Icons -->
                        <div class="social-icons" data-animate="social">
                            <a href="https://github.com" target="_blank" rel="noopener noreferrer" class="social-icon" aria-label="GitHub Profile">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                            <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" class="social-icon" aria-label="LinkedIn Profile">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" class="social-icon" aria-label="Twitter Profile">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                            <a href="mailto:<EMAIL>" class="social-icon" aria-label="Send Email">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h3.819l6.545 4.91 6.545-4.91h3.819A1.636 1.636 0 0 1 24 5.457z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Work Experience Section -->
    <section class="work-experience-section">
        <div class="container">
            <h2 class="section-title" data-animate="work-title">Work Experience</h2>

            <div class="timeline-container">
                <!-- Central Timeline Line -->
                <div class="timeline-line"></div>

                <!-- Timeline Item 1 - Left Side -->
                <div class="timeline-item timeline-item--left" data-animate="timeline-item-1">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="company-header">
                            <div class="company-logo">
                                <span class="logo-placeholder">CT</span>
                            </div>
                            <h3 class="company-name">Celebal Technologies</h3>
                        </div>
                        <h4 class="job-role">React Developer Intern</h4>
                        <p class="job-timeline">
                            <svg class="calendar-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                            </svg>
                            May 2025 - July 2025
                        </p>
                        <ul class="job-details">
                            <li>Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement.</li>
                            <li>Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS.</li>
                            <li><a href="#" class="certificate-btn" target="_blank" rel="noopener noreferrer">View Certificate</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Timeline Item 2 - Right Side -->
                <div class="timeline-item timeline-item--right" data-animate="timeline-item-2">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="company-header">
                            <div class="company-logo">
                                <span class="logo-placeholder">1S</span>
                            </div>
                            <h3 class="company-name">1stop.ai</h3>
                        </div>
                        <h4 class="job-role">Frontend Developer Intern</h4>
                        <p class="job-timeline">
                            <svg class="calendar-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                            </svg>
                            Jan 2025 - Apr 2025
                        </p>
                        <ul class="job-details">
                            <li>Collaborated with a team to develop responsive web application using MERN stack.</li>
                            <li>Implemented frontend components using React.js.</li>
                            <li>Assisted in database design and implementation with Firebase.</li>
                            <li>Integrated AWS S3 for media storage and content delivery.</li>
                            <li><a href="#" class="certificate-btn" target="_blank" rel="noopener noreferrer">View Certificate</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Work Section -->
    <section class="project-work-section">
        <div class="container">
            <h2 class="section-title" data-animate="project-title">Project Work</h2>

            <div class="project-timeline-container">
                <!-- Vertical Timeline Line -->
                <div class="project-timeline-line"></div>

                <!-- Project 1: Web accessibility analyser -->
                <div class="project-timeline-item" data-animate="project-item-1">
                    <div class="project-marker"></div>
                    <div class="project-content">
                        <h3 class="project-name">Web accessibility analyser</h3>
                        <p class="project-purpose">A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions.</p>
                        <div class="project-tech-stack">
                            <span class="tech-pill">React</span>
                            <span class="tech-pill">Node.js</span>
                            <span class="tech-pill">AI/ML</span>
                            <span class="tech-pill">Accessibility APIs</span>
                            <span class="tech-pill">Express.js</span>
                        </div>
                        <p class="project-learnings">Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers.</p>
                        <a href="https://github.com/Janmejay3108/Accessibility-analyzer" target="_blank" rel="noopener noreferrer" class="project-link" aria-label="View Web accessibility analyser on GitHub">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Project 2: Secure File Sharing Platform -->
                <div class="project-timeline-item" data-animate="project-item-2">
                    <div class="project-marker"></div>
                    <div class="project-content">
                        <h3 class="project-name">Secure File Sharing Platform</h3>
                        <p class="project-purpose">A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI.</p>
                        <div class="project-tech-stack">
                            <span class="tech-pill">React</span>
                            <span class="tech-pill">Node.js</span>
                            <span class="tech-pill">Express.js</span>
                            <span class="tech-pill">MongoDB</span>
                            <span class="tech-pill">Docker</span>
                            <span class="tech-pill">i18n</span>
                        </div>
                        <p class="project-learnings">Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker.</p>
                        <a href="https://github.com/Janmejay3108/File_Transfer_application" target="_blank" rel="noopener noreferrer" class="project-link" aria-label="View Secure File Sharing Platform on GitHub">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Education & Certificates Section -->
    <section class="education-certificates-section">
        <div class="container">
            <div id="education-certificates-root"></div>
        </div>
    </section>

    <script src="app.js"></script>

    <!-- Education & Certificates React Component -->
    <script type="text/babel">
      // Include the React component inline to avoid file loading issues
      const { useState, useEffect } = React;

      // Framer Motion fallback
      let motion, AnimatePresence;
      try {
        if (window.Motion) {
          motion = window.Motion.motion;
          AnimatePresence = window.Motion.AnimatePresence;
        }
      } catch (e) {
        console.log('Framer Motion not available, using fallback');
      }

      // Fallback components if Framer Motion is not available
      const fallbackMotion = {
        div: ({ children, className, style, initial, animate, exit, transition, variants, ...props }) =>
          React.createElement('div', { className, style, ...props }, children)
      };

      const fallbackAnimatePresence = ({ children }) => children;

      // Use Framer Motion if available, otherwise use fallbacks
      motion = motion || fallbackMotion;
      AnimatePresence = AnimatePresence || fallbackAnimatePresence;

      // Certificate data
      const certificatesData = [
        {
          id: 1,
          name: "AWS Cloud Solutions Architect",
          issuer: "Amazon Web Services",
          platform: "Forage",
          logo: "AWS"
        },
        {
          id: 2,
          name: "Full Stack Developer",
          issuer: "IBM",
          platform: "Coursera",
          logo: "IBM"
        },
        {
          id: 3,
          name: "Database and SQL for data science with Python",
          issuer: "IBM",
          platform: "Coursera",
          logo: "IBM"
        },
        {
          id: 4,
          name: "RAG and Agentic AI",
          issuer: "IBM",
          platform: "Coursera",
          logo: "IBM"
        },
        {
          id: 5,
          name: "Artificial Intelligence Essentials",
          issuer: "Google",
          platform: "Coursera",
          logo: "GGL"
        },
        {
          id: 6,
          name: "Convolutional Neural Networks",
          issuer: "Deeplearning.AI",
          platform: "Coursera",
          logo: "DL"
        }
      ];

      // Education data
      const educationData = [
        {
          id: 1,
          degree: "B.Tech in Electronics and Communication Engineering",
          institution: "Institute of Engineering and Management, Kolkata",
          details: "CGPA: 9.38 | Timeline: 2022-2026"
        },
        {
          id: 2,
          degree: "Class XII",
          institution: "Abhinav Bharati High School, Kolkata",
          details: "Score: 76.83% | Year: 2022"
        },
        {
          id: 3,
          degree: "Class X",
          institution: "Abhinav Bharati High School, Kolkata",
          details: "Score: 76% | Year: 2020"
        }
      ];

      // Certificates View Component
      const CertificatesView = () => {
        return React.createElement(motion.div, {
          key: "certificates",
          initial: { opacity: 0, x: 20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: -20 },
          transition: { duration: 0.5, ease: "easeInOut" },
          className: "timeline-view-container"
        }, [
          React.createElement('div', { key: 'line', className: 'vertical-timeline-line' }),
          React.createElement(motion.div, {
            key: 'items',
            initial: "hidden",
            animate: "visible",
            variants: {
              visible: {
                transition: {
                  staggerChildren: 0.1
                }
              }
            }
          }, certificatesData.map((cert, index) =>
            React.createElement(motion.div, {
              key: cert.id,
              variants: {
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 }
              },
              transition: { duration: 0.5, ease: "easeOut" },
              className: "timeline-view-item"
            }, [
              React.createElement('div', { key: 'marker', className: 'timeline-view-marker' }),
              React.createElement('div', { key: 'content', className: 'timeline-view-content' }, [
                React.createElement('h4', { key: 'name' }, cert.name),
                React.createElement('div', { key: 'details', className: 'certificate-details' }, [
                  React.createElement('div', { key: 'logo', className: 'certificate-logo' }, cert.logo),
                  React.createElement('span', { key: 'issuer', className: 'certificate-issuer' }, cert.issuer),
                  React.createElement('span', { key: 'platform', className: 'certificate-platform' }, cert.platform)
                ])
              ])
            ])
          ))
        ]);
      };

      // Education View Component
      const EducationView = () => {
        return React.createElement(motion.div, {
          key: "education",
          initial: { opacity: 0, x: 20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: -20 },
          transition: { duration: 0.5, ease: "easeInOut" },
          className: "timeline-view-container"
        }, [
          React.createElement('div', { key: 'line', className: 'vertical-timeline-line' }),
          React.createElement(motion.div, {
            key: 'items',
            initial: "hidden",
            animate: "visible",
            variants: {
              visible: {
                transition: {
                  staggerChildren: 0.1
                }
              }
            }
          }, educationData.map((edu, index) =>
            React.createElement(motion.div, {
              key: edu.id,
              variants: {
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 }
              },
              transition: { duration: 0.5, ease: "easeOut" },
              className: "timeline-view-item"
            }, [
              React.createElement('div', { key: 'marker', className: 'timeline-view-marker' }),
              React.createElement('div', { key: 'content', className: 'timeline-view-content' }, [
                React.createElement('h4', { key: 'degree' }, edu.degree),
                React.createElement('p', { key: 'institution', className: 'education-institution' }, edu.institution),
                React.createElement('p', { key: 'details', className: 'education-details' }, edu.details)
              ])
            ])
          ))
        ]);
      };

      // Main Education & Certificates Component
      const EducationCertificates = () => {
        const [activeTab, setActiveTab] = useState('certificates');

        return React.createElement('div', { className: 'education-certificates-container' }, [
          React.createElement('h2', { key: 'title', className: 'education-certificates-title' }, 'Education & Certificates'),
          React.createElement('div', { key: 'nav', className: 'tab-navigation' }, [
            React.createElement('button', {
              key: 'cert-btn',
              className: `tab-button ${activeTab === 'certificates' ? 'active' : ''}`,
              onClick: () => setActiveTab('certificates')
            }, 'Certificates'),
            React.createElement('button', {
              key: 'edu-btn',
              className: `tab-button ${activeTab === 'education' ? 'active' : ''}`,
              onClick: () => setActiveTab('education')
            }, 'Education')
          ]),
          React.createElement(AnimatePresence, { key: 'content', mode: 'wait' },
            activeTab === 'certificates' ?
              React.createElement(CertificatesView) :
              React.createElement(EducationView)
          )
        ]);
      };

      // Render the component
      const root = ReactDOM.createRoot(document.getElementById('education-certificates-root'));
      root.render(React.createElement(EducationCertificates));
    </script>
</body>
</html>