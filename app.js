// Portfolio Website JavaScript - Animation and Interaction Handler

class PortfolioAnimations {
    constructor() {
        this.initializeAnimations();
        this.setupInteractions();
    }

    // Initialize staggered reveal animations on page load
    initializeAnimations() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.startStaggeredReveal();
            });
        } else {
            this.startStaggeredReveal();
        }
    }

    // Start the staggered reveal animation sequence
    startStaggeredReveal() {
        const animatedElements = [
            { selector: '[data-animate="name"]', delay: 100 },
            { selector: '[data-animate="bio"]', delay: 200 },
            { selector: '[data-animate="buttons"]', delay: 300 },
            { selector: '[data-animate="social"]', delay: 400 }
        ];

        animatedElements.forEach(({ selector, delay }) => {
            const element = document.querySelector(selector);
            if (element) {
                setTimeout(() => {
                    element.classList.add('animate-in');
                }, delay);
            }
        });

        // Setup scroll-triggered animations for Work Experience section
        this.setupWorkExperienceAnimations();

        // Setup scroll-triggered animations for Project Work section
        this.setupProjectWorkAnimations();

        // Setup scroll-triggered animations for Education & Certificates section
        this.setupEducationCertificatesAnimations();

        // Setup scroll-triggered animations for Skills section
        this.setupSkillsAnimations();

        // Setup scroll-triggered animations for Skills section
        this.setupSkillsAnimations();
    }

    // Setup button and social icon interactions
    setupInteractions() {
        this.setupButtonInteractions();
        this.setupSocialIconInteractions();
    }

    // Setup Work Experience section scroll-triggered animations
    setupWorkExperienceAnimations() {
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe section title
        const sectionTitle = document.querySelector('[data-animate="work-title"]');
        if (sectionTitle) {
            observer.observe(sectionTitle);
        }

        // Observe timeline items with staggered delays
        const timelineItems = [
            { selector: '[data-animate="timeline-item-1"]', delay: 200 },
            { selector: '[data-animate="timeline-item-2"]', delay: 400 }
        ];

        timelineItems.forEach(({ selector, delay }) => {
            const element = document.querySelector(selector);
            if (element) {
                const itemObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                entry.target.classList.add('animate-in');
                            }, delay);
                            itemObserver.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                itemObserver.observe(element);
            }
        });
    }

    // Setup Project Work section scroll-triggered animations
    setupProjectWorkAnimations() {
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe section title
        const projectTitle = document.querySelector('[data-animate="project-title"]');
        if (projectTitle) {
            observer.observe(projectTitle);
        }

        // Observe project items with staggered delays
        const projectItems = [
            { selector: '[data-animate="project-item-1"]', delay: 200 },
            { selector: '[data-animate="project-item-2"]', delay: 400 }
        ];

        projectItems.forEach(({ selector, delay }) => {
            const element = document.querySelector(selector);
            if (element) {
                const itemObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                entry.target.classList.add('animate-in');
                            }, delay);
                            itemObserver.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                itemObserver.observe(element);
            }
        });
    }

    // Setup Education & Certificates section scroll-triggered animations and tab functionality
    setupEducationCertificatesAnimations() {
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe section title
        const educationTitle = document.querySelector('[data-animate="education-title"]');
        if (educationTitle) {
            observer.observe(educationTitle);
        }

        // Observe tab navigation
        const tabNav = document.querySelector('[data-animate="tab-nav"]');
        if (tabNav) {
            observer.observe(tabNav);
        }

        // Setup tab functionality
        this.setupTabFunctionality();

        // Setup initial certificate items animation
        this.setupTimelineItemsAnimation('certificates');
    }

    // Setup tab switching functionality
    setupTabFunctionality() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');

                // Update active button
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Handle tab content transition
                const currentActive = document.querySelector('.tab-content.active');
                const targetContent = document.querySelector(`[data-content="${targetTab}"]`);

                if (currentActive && targetContent && currentActive !== targetContent) {
                    // Add exiting class for smooth transition
                    currentActive.classList.add('exiting');

                    setTimeout(() => {
                        currentActive.classList.remove('active', 'exiting');
                        targetContent.classList.add('active');

                        // Animate timeline items for the new tab
                        this.setupTimelineItemsAnimation(targetTab);
                    }, 250);
                }
            });
        });
    }

    // Setup timeline items animation for specific tab
    setupTimelineItemsAnimation(tabType) {
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -10% 0px'
        };

        // Define items based on tab type
        const items = tabType === 'certificates'
            ? [
                { selector: '[data-animate="cert-item-1"]', delay: 100 },
                { selector: '[data-animate="cert-item-2"]', delay: 200 },
                { selector: '[data-animate="cert-item-3"]', delay: 300 },
                { selector: '[data-animate="cert-item-4"]', delay: 400 },
                { selector: '[data-animate="cert-item-5"]', delay: 500 },
                { selector: '[data-animate="cert-item-6"]', delay: 600 }
            ]
            : [
                { selector: '[data-animate="edu-item-1"]', delay: 100 },
                { selector: '[data-animate="edu-item-2"]', delay: 200 },
                { selector: '[data-animate="edu-item-3"]', delay: 300 }
            ];

        items.forEach(({ selector, delay }) => {
            const element = document.querySelector(selector);
            if (element) {
                const itemObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                entry.target.classList.add('animate-in');
                            }, delay);
                            itemObserver.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                itemObserver.observe(element);
            }
        });
    }

    // Setup Skills section scroll-triggered animations and word cloud
    setupSkillsAnimations() {
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe section title
        const skillsTitle = document.querySelector('[data-animate="skills-title"]');
        if (skillsTitle) {
            observer.observe(skillsTitle);
        }

        // Observe word cloud
        const wordcloud = document.querySelector('[data-animate="wordcloud"]');
        if (wordcloud) {
            observer.observe(wordcloud);
        }

        // Setup skill category items with staggered delays
        const skillCategories = [
            { selector: '[data-animate="skill-category-1"]', delay: 100 },
            { selector: '[data-animate="skill-category-2"]', delay: 200 },
            { selector: '[data-animate="skill-category-3"]', delay: 300 },
            { selector: '[data-animate="skill-category-4"]', delay: 400 },
            { selector: '[data-animate="skill-category-5"]', delay: 500 },
            { selector: '[data-animate="skill-category-6"]', delay: 600 }
        ];

        skillCategories.forEach(({ selector, delay }) => {
            const element = document.querySelector(selector);
            if (element) {
                const itemObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                entry.target.classList.add('animate-in');
                            }, delay);
                            itemObserver.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                itemObserver.observe(element);
            }
        });

        // Setup word cloud 3D positioning and mouse interaction
        this.setupWordCloud();
    }

    // Setup 3D word cloud positioning and interactions
    setupWordCloud() {
        const wordcloudSphere = document.getElementById('wordcloud-sphere');
        const wordcloudItems = document.querySelectorAll('.wordcloud-item');

        if (!wordcloudSphere || wordcloudItems.length === 0) return;

        // Position items in 3D sphere
        this.positionWordCloudItems(wordcloudItems);

        // Add mouse interaction
        this.setupWordCloudMouseInteraction(wordcloudSphere);
    }

    // Position word cloud items in 3D sphere
    positionWordCloudItems(items) {
        const radius = 180;
        const total = items.length;

        items.forEach((item, index) => {
            // Calculate spherical coordinates
            const phi = Math.acos(-1 + (2 * index) / total);
            const theta = Math.sqrt(total * Math.PI) * phi;

            // Convert to Cartesian coordinates
            const x = radius * Math.cos(theta) * Math.sin(phi);
            const y = radius * Math.sin(theta) * Math.sin(phi);
            const z = radius * Math.cos(phi);

            // Apply 3D transform
            item.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;

            // Add random animation delay for staggered appearance
            item.style.animationDelay = `${Math.random() * 2}s`;
        });
    }

    // Setup mouse interaction for word cloud
    setupWordCloudMouseInteraction(sphere) {
        let mouseX = 0;
        let mouseY = 0;
        let isHovering = false;

        const container = sphere.closest('.wordcloud-container');
        if (!container) return;

        container.addEventListener('mouseenter', () => {
            isHovering = true;
            sphere.style.animationPlayState = 'paused';
        });

        container.addEventListener('mouseleave', () => {
            isHovering = false;
            sphere.style.animationPlayState = 'running';
            // Reset to original rotation
            sphere.style.transform = '';
        });

        container.addEventListener('mousemove', (e) => {
            if (!isHovering) return;

            const rect = container.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            mouseX = (e.clientX - centerX) / rect.width;
            mouseY = (e.clientY - centerY) / rect.height;

            // Apply subtle rotation based on mouse position
            const rotateY = mouseX * 30; // Max 30 degrees
            const rotateX = -mouseY * 30; // Max 30 degrees

            sphere.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
    }

    // Enhanced button interactions with scale and glow effects
    setupButtonInteractions() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            // Mouse/touch events for scale effects
            button.addEventListener('mouseenter', () => {
                this.scaleElement(button, 1.05);
            });

            button.addEventListener('mouseleave', () => {
                this.scaleElement(button, 1);
            });

            button.addEventListener('mousedown', () => {
                this.scaleElement(button, 0.95);
            });

            button.addEventListener('mouseup', () => {
                this.scaleElement(button, 1.05);
            });

            // Touch events for mobile
            button.addEventListener('touchstart', (e) => {
                this.scaleElement(button, 0.95);
            });

            button.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    this.scaleElement(button, 1);
                }, 150);
            });

            // Handle View Work button click
            if (button.classList.contains('btn-view-work')) {
                button.addEventListener('click', () => {
                    this.handleViewWorkClick();
                });
            }
        });
    }

    // Enhanced social icon interactions
    setupSocialIconInteractions() {
        const socialIcons = document.querySelectorAll('.social-icon');
        
        socialIcons.forEach(icon => {
            // Mouse events
            icon.addEventListener('mouseenter', () => {
                this.scaleElement(icon, 1.2);
                this.highlightIcon(icon, true);
            });

            icon.addEventListener('mouseleave', () => {
                this.scaleElement(icon, 1);
                this.highlightIcon(icon, false);
            });

            // Touch events for mobile
            icon.addEventListener('touchstart', () => {
                this.scaleElement(icon, 1.1);
                this.highlightIcon(icon, true);
            });

            icon.addEventListener('touchend', () => {
                setTimeout(() => {
                    this.scaleElement(icon, 1);
                    this.highlightIcon(icon, false);
                }, 200);
            });

            // Add click analytics
            icon.addEventListener('click', (e) => {
                this.trackSocialClick(e.currentTarget);
            });
        });
    }

    // Utility function to scale elements smoothly
    scaleElement(element, scale) {
        element.style.transform = `scale(${scale})`;
    }

    // Highlight social icons with accent color
    highlightIcon(icon, highlight) {
        if (highlight) {
            icon.style.color = '#64ffda';
            icon.style.borderColor = '#64ffda';
        } else {
            icon.style.color = '';
            icon.style.borderColor = '';
        }
    }

    // Handle View Work button click (placeholder for portfolio navigation)
    handleViewWorkClick() {
        // Add a visual feedback animation
        const button = document.querySelector('.btn-view-work');
        button.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            button.style.transform = '';
        }, 150);

        // Placeholder for navigation - could scroll to portfolio section
        console.log('View Work clicked - Navigate to portfolio section');
        
        // You can add actual navigation logic here, such as:
        // window.location.href = '#portfolio';
        // or smooth scroll to a portfolio section
    }

    // Track social media clicks for analytics (placeholder)
    trackSocialClick(iconElement) {
        const href = iconElement.getAttribute('href');
        let platform = 'unknown';
        
        if (href.includes('github')) platform = 'github';
        else if (href.includes('linkedin')) platform = 'linkedin';
        else if (href.includes('twitter')) platform = 'twitter';
        else if (href.includes('mailto')) platform = 'email';
        
        console.log(`Social click tracked: ${platform}`);
        
        // Add your analytics tracking here
        // gtag('event', 'social_click', { platform: platform });
    }
}

// Additional utility functions for enhanced user experience
class PortfolioUtils {
    constructor() {
        this.setupKeyboardNavigation();
        this.setupScrollEffects();
        this.setupResponsiveAdjustments();
    }

    // Setup keyboard navigation for accessibility
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // ESC key to remove focus from any focused element
            if (e.key === 'Escape') {
                document.activeElement?.blur();
            }
            
            // Tab navigation enhancement
            if (e.key === 'Tab') {
                this.highlightFocusableElements();
            }
        });
    }

    // Highlight focusable elements for better accessibility
    highlightFocusableElements() {
        const focusableElements = document.querySelectorAll('button, a, [tabindex]:not([tabindex="-1"])');
        focusableElements.forEach(el => {
            el.addEventListener('focus', () => {
                el.style.boxShadow = '0 0 0 3px rgba(100, 255, 218, 0.4)';
            });
            
            el.addEventListener('blur', () => {
                el.style.boxShadow = '';
            });
        });
    }

    // Setup subtle scroll effects (if content extends beyond viewport)
    setupScrollEffects() {
        let ticking = false;
        
        const updateScrollEffects = () => {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            
            if (heroSection) {
                // Subtle parallax effect on background gradient
                heroSection.style.transform = `translateY(${scrolled * 0.1}px)`;
            }
            
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        });
    }

    // Responsive behavior adjustments
    setupResponsiveAdjustments() {
        const mediaQuery = window.matchMedia('(max-width: 767px)');
        
        const handleMediaQueryChange = (e) => {
            const buttons = document.querySelectorAll('.btn');
            const socialIcons = document.querySelectorAll('.social-icon');
            
            if (e.matches) {
                // Mobile optimizations
                buttons.forEach(btn => {
                    btn.style.width = '100%';
                    btn.style.maxWidth = '200px';
                });
            } else {
                // Desktop optimizations
                buttons.forEach(btn => {
                    btn.style.width = 'auto';
                    btn.style.maxWidth = 'none';
                });
            }
        };
        
        mediaQuery.addListener(handleMediaQueryChange);
        handleMediaQueryChange(mediaQuery); // Initial check
    }
}

// Performance optimization: Intersection Observer for animations
class PerformanceOptimizer {
    constructor() {
        this.setupIntersectionObserver();
    }

    setupIntersectionObserver() {
        // Only run animations when elements are in viewport
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -10% 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Element is in viewport
                    entry.target.classList.add('in-viewport');
                } else {
                    entry.target.classList.remove('in-viewport');
                }
            });
        }, observerOptions);

        // Observe hero section
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            observer.observe(heroSection);
        }

        // Observe work experience section
        const workSection = document.querySelector('.work-experience-section');
        if (workSection) {
            observer.observe(workSection);
        }

        // Observe project work section
        const projectSection = document.querySelector('.project-work-section');
        if (projectSection) {
            observer.observe(projectSection);
        }

        // Observe education & certificates section
        const educationSection = document.querySelector('.education-certificates-section');
        if (educationSection) {
            observer.observe(educationSection);
        }

        // Observe skills section
        const skillsSection = document.querySelector('.skills-section');
        if (skillsSection) {
            observer.observe(skillsSection);
        }
    }
}

// Initialize all classes when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Initialize main animation controller
    new PortfolioAnimations();
    
    // Initialize utility functions
    new PortfolioUtils();
    
    // Initialize performance optimizations
    new PerformanceOptimizer();
    
    // Add loading complete class to body for any final CSS transitions
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 1000);
});

// Handle page visibility changes for performance
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden, pause any intensive animations
        document.body.classList.add('page-hidden');
    } else {
        // Page is visible, resume animations
        document.body.classList.remove('page-hidden');
    }
});

// Smooth scroll polyfill for older browsers
if (!('scrollBehavior' in document.documentElement.style)) {
    const smoothScrollPolyfill = () => {
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    };
    
    smoothScrollPolyfill();
}